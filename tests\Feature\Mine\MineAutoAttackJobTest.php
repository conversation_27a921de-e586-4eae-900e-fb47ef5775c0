<?php

namespace Tests\Feature\Mine;

use Tests\TestCase;
use App\Jobs\MineAutoAttackJob;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;

class MineAutoAttackJobTest extends TestCase
{
    public function test_mine_auto_attack_job_execution()
    {
        // Мокаем логи для отслеживания выполнения
        Log::shouldReceive('info')->andReturnUsing(function ($message, $context = []) {
            echo "LOG: $message " . json_encode($context) . "\n";
            return true;
        });

        Log::shouldReceive('error')->andReturnUsing(function ($message, $context = []) {
            echo "ERROR: $message " . json_encode($context) . "\n";
            return true;
        });

        // Запускаем Job через диспетчер
        try {
            // Используем синхронную очередь для тестирования
            Queue::fake();

            // Диспетчим Job
            MineAutoAttackJob::dispatch();

            // Проверяем, что Job был добавлен в очередь
            Queue::assertPushed(MineAutoAttackJob::class);

            $this->assertTrue(true, 'Job dispatched successfully');
        } catch (\Exception $e) {
            echo "Exception: " . $e->getMessage() . "\n";
            echo "Trace: " . $e->getTraceAsString() . "\n";
            $this->fail('Job failed with exception: ' . $e->getMessage());
        }
    }
}
