<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MobSkillTemplate;

class UpdateMobSkillTemplate extends Command
{
    protected $signature = 'mob:update-skill-template {name} {--chance=} {--duration=}';
    protected $description = 'Обновить шаблон скилла моба';

    public function handle()
    {
        $name = $this->argument('name');

        // Находим шаблон по имени
        $template = MobSkillTemplate::where('name', $name)->first();
        if (!$template) {
            $this->error("Шаблон скилла '{$name}' не найден");
            return 1;
        }

        $this->info("Найден шаблон скилла: {$template->name} (ID: {$template->id})");
        $this->info("Текущие данные:");
        $this->info("- Chance: " . ($template->chance ?? 'NULL'));
        $this->info("- Duration: " . ($template->duration ?? 'NULL'));
        $this->info("- Effect Data: " . json_encode($template->effect_data, JSON_UNESCAPED_UNICODE));

        $updated = false;

        if ($this->option('chance') !== null) {
            $chance = (int) $this->option('chance');
            $template->chance = $chance;
            $this->info("Установлен шанс срабатывания: {$chance}%");
            $updated = true;
        }

        if ($this->option('duration') !== null) {
            $duration = (int) $this->option('duration');
            $template->duration = $duration;
            $this->info("Установлена длительность: {$duration} сек");
            $updated = true;
        }

        if ($updated) {
            $template->save();
            $this->info("\n✅ Шаблон скилла успешно обновлен!");
            $template->refresh();
            $this->info("Новые данные:");
            $this->info("- Chance: " . $template->chance);
            $this->info("- Duration: " . $template->duration);
        } else {
            $this->info("Нет изменений для применения");
        }

        return 0;
    }
}
