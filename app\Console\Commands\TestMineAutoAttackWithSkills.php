<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Mob;
use App\Models\MineLocation;
use App\Services\Mine\MobSkillIntegrationService;
use App\Services\PlayerHealthService;
use App\Services\CombatFormulaService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestMineAutoAttackWithSkills extends Command
{
    protected $signature = 'test:mine-auto-attack-skills';
    protected $description = 'Тестирует автоатаку в руднике с активацией скиллов';

    public function handle()
    {
        $this->info("🧪 Тестирование автоатаки в руднике с скиллами...");

        // Находим игрока admin
        $player = User::where('name', 'admin')->first();
        if (!$player) {
            $this->error("Игрок admin не найден!");
            return 1;
        }

        // Находим моба Огр в руднике
        $mob = Mob::find(14);
        if (!$mob) {
            $this->error("Моб с ID 14 не найден!");
            return 1;
        }

        // Находим локацию рудника
        $mineLocation = MineLocation::find(172);
        if (!$mineLocation) {
            $this->error("Локация рудника с ID 172 не найдена!");
            return 1;
        }

        $this->info("✅ Найдены:");
        $this->info("  Игрок: {$player->name} (ID: {$player->id})");
        $this->info("  Моб: {$mob->name} (ID: {$mob->id})");
        $this->info("  Рудник: {$mineLocation->name} (ID: {$mineLocation->id})");

        // Проверяем скиллы моба
        $skills = $mob->skills()->with('skillTemplate')->get();
        $this->info("  Скиллы моба: " . $skills->count());
        foreach ($skills as $skill) {
            $template = $skill->skillTemplate;
            $this->info("    - {$template->name} (Chance: {$template->chance}%, Cooldown: " . ($skill->cooldown_ends_at ?? 'None') . ")");
        }

        $this->info("\n⚔️ Выполняем атаку...");

        // Получаем HP до атаки
        $hpBefore = app(PlayerHealthService::class)->getCurrentHP($player);
        $this->info("HP до атаки: {$hpBefore}");

        // Рассчитываем урон
        $combatService = app(CombatFormulaService::class);
        $baseDamage = rand(5, 15);
        $damage = $combatService->calculateDamage($mob->strength ?? 10, $player->profile->getEffectiveStats()['armor'] ?? 0);
        $this->info("Рассчитанный урон: {$damage}");

        // Применяем урон
        $healthService = app(PlayerHealthService::class);
        $damageResult = $healthService->applyDamage($player, $damage, "test_mine_attack:{$mob->id}");
        $this->info("Урон применен: {$damageResult['damage']}");

        // Обрабатываем скиллы моба
        $this->info("\n🎯 Обрабатываем скиллы моба...");
        $mobSkillService = app(MobSkillIntegrationService::class);

        $activatedSkills = $mobSkillService->processMobAttackSkills($mob, $player, [
            'mine_location_id' => $mineLocation->id,
            'location_id' => $mineLocation->location_id,
            'damage_dealt' => $damageResult['damage'],
            'attack_type' => 'test_mine_attack'
        ]);

        $this->info("Активированных скиллов: " . count($activatedSkills));

        foreach ($activatedSkills as $skill) {
            $this->info("✅ Активирован скилл:");
            $this->info("  Тип: " . ($skill['skill_type'] ?? 'Unknown'));
            $this->info("  Успех: " . ($skill['success'] ? 'Да' : 'Нет'));
            $this->info("  Сообщение: " . ($skill['message'] ?? 'Нет'));
            if (isset($skill['effect_id'])) {
                $this->info("  Effect ID: " . $skill['effect_id']);
            }
        }

        // Проверяем HP после атаки
        $hpAfter = app(PlayerHealthService::class)->getCurrentHP($player);
        $this->info("\nHP после атаки: {$hpAfter}");

        // Проверяем активные эффекты
        $this->info("\n🔍 Проверяем активные эффекты...");
        $effects = $player->activeEffects()->get();
        $this->info("Активных эффектов: " . $effects->count());

        foreach ($effects as $effect) {
            $this->info("✅ Эффект:");
            $this->info("  ID: {$effect->id}");
            $this->info("  Тип: {$effect->effect_type}");
            $this->info("  Длительность: {$effect->duration} сек");
            $this->info("  Заканчивается: {$effect->ends_at}");
            $this->info("  Данные: " . json_encode($effect->effect_data, JSON_UNESCAPED_UNICODE));
        }

        $this->info("\n🎯 Тест завершен!");
        return 0;
    }
}
