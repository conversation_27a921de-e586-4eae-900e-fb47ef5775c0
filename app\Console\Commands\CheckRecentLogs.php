<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class CheckRecentLogs extends Command
{
    protected $signature = 'logs:recent {--filter=}';
    protected $description = 'Показывает последние записи из лога';

    public function handle()
    {
        $filter = $this->option('filter');
        $logPath = storage_path('logs/laravel.log');
        
        if (!file_exists($logPath)) {
            $this->error("Лог файл не найден: {$logPath}");
            return 1;
        }

        // Читаем последние 50 строк
        $lines = [];
        $file = fopen($logPath, 'r');
        
        if ($file) {
            // Переходим к концу файла
            fseek($file, -8192, SEEK_END); // Читаем последние 8KB
            $content = fread($file, 8192);
            fclose($file);
            
            $lines = explode("\n", $content);
            $lines = array_slice($lines, -50); // Последние 50 строк
        }

        $filteredLines = [];
        foreach ($lines as $line) {
            if (empty($line)) continue;
            
            if ($filter) {
                if (stripos($line, $filter) !== false) {
                    $filteredLines[] = $line;
                }
            } else {
                $filteredLines[] = $line;
            }
        }

        $this->info("Последние записи лога" . ($filter ? " (фильтр: {$filter})" : "") . ":");
        $this->info("");
        
        foreach ($filteredLines as $line) {
            $this->line($line);
        }

        return 0;
    }
}
