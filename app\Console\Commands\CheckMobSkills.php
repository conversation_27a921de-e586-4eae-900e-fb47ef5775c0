<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Mob;

class CheckMobSkills extends Command
{
    protected $signature = 'mob:check-skills {mob_name}';
    protected $description = 'Проверить скиллы моба';

    public function handle()
    {
        $mobName = $this->argument('mob_name');

        // Находим моба
        $mob = Mob::where('name', $mobName)->first();
        if (!$mob) {
            $this->error("Моб '{$mobName}' не найден");
            return 1;
        }

        $this->info("Проверка скиллов для моба: {$mob->name} (ID: {$mob->id})");

        // Получаем скиллы моба
        $skills = $mob->skills()->with('skillTemplate')->get();
        
        $this->info("Количество скиллов: " . $skills->count());
        
        if ($skills->count() > 0) {
            foreach ($skills as $skill) {
                $this->info("  - Скилл ID: {$skill->id}, Template ID: {$skill->skill_template_id}");
                if ($skill->skillTemplate) {
                    $this->info("    Название: {$skill->skillTemplate->name}");
                    $this->info("    Тип эффекта: {$skill->skillTemplate->effect_type}");
                    $this->info("    Шанс: {$skill->skillTemplate->trigger_chance}%");
                    $this->info("    Кулдаун: {$skill->skillTemplate->cooldown} сек");
                    $this->info("    Длительность: {$skill->skillTemplate->effect_duration} сек");
                    $this->info("    Значение эффекта: {$skill->skillTemplate->effect_value}");
                } else {
                    $this->error("    Шаблон скилла не найден!");
                }
                $this->info("    ---");
            }
        } else {
            $this->info("  У моба нет скиллов");
        }

        return 0;
    }
}
