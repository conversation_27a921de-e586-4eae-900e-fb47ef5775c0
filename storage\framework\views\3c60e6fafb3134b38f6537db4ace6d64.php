<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames(([
    'invitation' => null,
    'compact' => false
]));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter(([
    'invitation' => null,
    'compact' => false
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php
// Проверяем активные приглашения в группы
$activeInvitation = null;
if (Auth::check()) {
    $activeInvitation = Auth::user()->partyInvitations()
        ->with(['inviter.profile'])
        ->active()
        ->latest()
        ->first();
}

// Если передано конкретное приглашение, используем его
if ($invitation) {
    $activeInvitation = $invitation;
}
?>

<?php if($activeInvitation): ?>
    <?php
        $inviter = $activeInvitation->inviter;
        $inviterProfile = $inviter->profile;
        $playerClassName = $inviterProfile->class ?? 'warrior';
        
        // Получаем иконку класса и расы
        $raceClassIcon = getRaceClassIcon($inviterProfile->race ?? 'solarius', $inviterProfile->class ?? 'warrior');
        
        // Расчет времени до истечения
        $expiresAt = $activeInvitation->expires_at;
        $timeLeft = $expiresAt->diffInSeconds(now());
        $timeLeftFormatted = sprintf('%02d:%02d', intval($timeLeft / 60), $timeLeft % 60);
        
        // Определяем размер компонента
        $sizeClasses = $compact ? 'text-xs px-2 py-1' : 'text-sm px-3 py-2';
        $avatarSize = $compact ? 'w-8 h-8' : 'w-12 h-12';
        $iconSize = $compact ? 'text-sm' : 'text-base';
    ?>

    <div class="party-invitation-notification <?php echo e($compact ? 'compact' : ''); ?> mt-2 mb-2 mx-2" 
         data-invitation-id="<?php echo e($activeInvitation->id); ?>"
         data-expires-at="<?php echo e($expiresAt->timestamp); ?>">
        
        
        <div class="invitation-container bg-gradient-to-b from-[#3d3a2e] to-[#2a2721] 
                    border-2 border-[#514b3c] rounded-lg shadow-lg backdrop-blur-sm
                    animate-pulse-glow <?php echo e($sizeClasses); ?>">
            
            
            <div class="invitation-header flex items-center justify-between mb-2">
                <div class="flex items-center space-x-2">
                    <span class="invitation-icon text-[#a6925e] <?php echo e($iconSize); ?>"></span>
                    <span class="invitation-title text-[#fceac4] font-bold <?php echo e($compact ? 'text-xs' : 'text-sm'); ?>">
                        Приглашение в группу
                    </span>
                </div>
                <div class="timer-container flex items-center space-x-1">
                    <span class="timer-icon text-red-400 <?php echo e($iconSize); ?>"></span>
                    <span class="timer-text text-red-300 font-mono <?php echo e($compact ? 'text-xs' : 'text-sm'); ?>" 
                          id="timer-<?php echo e($activeInvitation->id); ?>"><?php echo e($timeLeftFormatted); ?></span>
                </div>
            </div>

            
            <div class="player-info flex items-center space-x-3 mb-3">
                
                <?php
                    // Определяем путь к аватару на основе расы
                    $avatarPath = 'assets/character-placeholder.png'; // Заглушка по умолчанию
                    if ($inviterProfile && $inviterProfile->race === 'solarius') {
                        $avatarPath = 'assets/avatar/hero-solarius.jpg';
                    } elseif ($inviterProfile && $inviterProfile->race === 'lunarius') {
                        $avatarPath = 'assets/avatar/hero-lunarius.jpg';
                    }
                ?>
                <div class="player-avatar relative flex-shrink-0">
                    <?php if(file_exists(public_path($avatarPath))): ?>
                        <img src="<?php echo e(asset($avatarPath)); ?>" 
                             alt="Аватар <?php echo e($inviter->name); ?>" 
                             class="<?php echo e($avatarSize); ?> object-cover rounded-lg border-2 border-[#3b3629] shadow-md shadow-black/50 transition-all duration-300">
                    <?php else: ?>
                        
                        <div class="<?php echo e($avatarSize); ?> bg-gradient-to-br from-[#3b3629] to-[#2a2722] flex items-center justify-center rounded-lg border-2 border-[#3b3629] shadow-md shadow-black/50 transition-all duration-300">
                            <span class="text-[#e4d7b0] font-bold <?php echo e($compact ? 'text-xs' : 'text-lg'); ?>">
                                <?php echo e(strtoupper(substr($inviter->name, 0, 1))); ?>

                            </span>
                        </div>
                    <?php endif; ?>
                </div>

                
                <div class="player-details flex-1">
                    <div class="player-name text-[#fceac4] font-bold <?php echo e($compact ? 'text-sm' : 'text-base'); ?>">
                        <?php echo e($inviter->name); ?>

                    </div>
                    <div class="player-stats flex items-center space-x-3 <?php echo e($compact ? 'text-xs' : 'text-sm'); ?>">
                        
                        <div class="stat-item flex items-center space-x-1">
                            <span class="stat-icon text-[#a6925e]"></span>
                            <span class="stat-label text-[#e4d7b0]">Ур:</span>
                            <span class="stat-value text-[#fceac4] font-semibold"><?php echo e($inviterProfile->level ?? 1); ?></span>
                        </div>
                        
                        <div class="stat-item flex items-center space-x-1">
                            <span class="stat-icon text-[#a6925e]"></span>
                            <span class="stat-label text-[#e4d7b0]">GS:</span>
                            <span class="stat-value text-[#fceac4] font-semibold"><?php echo e($inviterProfile->GS ?? 0); ?></span>
                        </div>
                        
                        <?php if($playerClassName): ?>
                        <div class="stat-item flex items-center space-x-1">
                            <img src="<?php echo e($raceClassIcon); ?>" 
                                 alt="<?php echo e(getClassDisplayName($playerClassName)); ?>"
                                 class="<?php echo e($compact ? 'w-3 h-3' : 'w-4 h-4'); ?>">
                            <span class="stat-value text-[#fceac4] font-semibold"><?php echo e(getClassDisplayName($playerClassName)); ?></span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            
            <?php if($activeInvitation->message): ?>
            <div class="invitation-message bg-[#1a1814]/80 rounded p-2 mb-3 border border-[#514b3c]/50">
                <div class="message-icon text-[#a6925e] mb-1 <?php echo e($iconSize); ?>"></div>
                <div class="message-text text-[#fceac4] <?php echo e($compact ? 'text-xs' : 'text-sm'); ?> italic">
                    "<?php echo e($activeInvitation->message); ?>"
                </div>
            </div>
            <?php endif; ?>

            
            <div class="action-buttons flex space-x-2">
                <form method="POST" action="<?php echo e(route('party.invitations.accept', $activeInvitation->id)); ?>" 
                      class="flex-1" onsubmit="return handleInvitationAction(this, 'accept')">
                    <?php echo csrf_field(); ?>
                    <button type="submit" 
                            class="accept-btn w-full bg-gradient-to-r from-[#2f473c] to-[#1e2e27] 
                                   text-[#fceac4] <?php echo e($compact ? 'py-1 px-2 text-xs' : 'py-2 px-3 text-sm'); ?> 
                                   rounded font-semibold border border-[#a6925e] 
                                   hover:from-[#3b5147] hover:to-[#2a3b32] hover:border-[#b8a469]
                                   transition-all duration-200 transform hover:scale-105
                                   shadow-lg hover:shadow-[#a6925e]/25">
                        <span class="flex items-center justify-center space-x-1">
                            <span>✅</span>
                            <span>Принять</span>
                        </span>
                    </button>
                </form>
                
                <form method="POST" action="<?php echo e(route('party.invitations.decline', $activeInvitation->id)); ?>" 
                      class="flex-1" onsubmit="return handleInvitationAction(this, 'decline')">
                    <?php echo csrf_field(); ?>
                    <button type="submit" 
                            class="decline-btn w-full bg-gradient-to-r from-[#4a1f1a] to-[#5e2b26] 
                                   text-[#d4675a] <?php echo e($compact ? 'py-1 px-2 text-xs' : 'py-2 px-3 text-sm'); ?> 
                                   rounded font-semibold border border-[#6b3129] 
                                   hover:from-[#5e2b26] hover:to-[#6b3129] hover:border-[#7d3a30]
                                   transition-all duration-200 transform hover:scale-105
                                   shadow-lg hover:shadow-[#d4675a]/25">
                        <span class="flex items-center justify-center space-x-1">
                            <span>❌</span>
                            <span>Отклонить</span>
                        </span>
                    </button>
                </form>
            </div>
        </div>
    </div>

    
    <?php if (! $__env->hasRenderedOnce('532e4417-4495-4ecc-a37d-b3d5e7943dc8')): $__env->markAsRenderedOnce('532e4417-4495-4ecc-a37d-b3d5e7943dc8'); ?>
    <?php $__env->startPush('styles'); ?>
    <style>
        .party-invitation-notification {
            position: relative;
            z-index: 1000;
        }

        .invitation-container {
            position: relative;
            overflow: hidden;
        }

        .invitation-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        @keyframes pulse-glow {
            0%, 100% {
                box-shadow: 0 0 20px rgba(166, 146, 94, 0.3);
                border-color: #514b3c;
            }
            50% {
                box-shadow: 0 0 30px rgba(166, 146, 94, 0.5);
                border-color: #a6925e;
            }
        }

        .animate-pulse-glow {
            animation: pulse-glow 2s ease-in-out infinite;
        }

        .party-invitation-notification.compact .invitation-container {
            padding: 8px 12px;
        }

        .timer-text {
            min-width: 40px;
            text-align: center;
        }

        .timer-text.warning {
            color: #fbbf24 !important;
            animation: blink 1s infinite;
        }

        .timer-text.critical {
            color: #ef4444 !important;
            animation: blink 0.5s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        .player-avatar {
            filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3));
        }

        .action-buttons button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        .stat-item {
            background: rgba(26, 24, 20, 0.8);
            padding: 2px 6px;
            border-radius: 4px;
            border: 1px solid rgba(166, 146, 94, 0.3);
        }

        /* Темная фэнтези тематика */
        .invitation-container {
            background-image: 
                radial-gradient(circle at 20% 50%, rgba(166, 146, 94, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 50%, rgba(166, 146, 94, 0.1) 0%, transparent 50%),
                linear-gradient(135deg, rgba(26, 24, 20, 0.2) 0%, rgba(61, 58, 46, 0.1) 100%);
        }

        /* Адаптивность для мобильных устройств */
        @media (max-width: 640px) {
            .party-invitation-notification {
                margin: 8px;
            }
            
            .player-stats {
                flex-wrap: wrap;
                gap: 4px;
            }
            
            .stat-item {
                padding: 1px 4px;
                font-size: 0.75rem;
            }
        }
    </style>
    <?php $__env->stopPush(); ?>
    <?php endif; ?>

    
    <?php if (! $__env->hasRenderedOnce('a8bdee78-ea89-4587-9920-b90c4a704ab5')): $__env->markAsRenderedOnce('a8bdee78-ea89-4587-9920-b90c4a704ab5'); ?>
    <?php $__env->startPush('scripts'); ?>
    <script>
        // Обновление таймера
        function updateInvitationTimers() {
            document.querySelectorAll('[data-expires-at]').forEach(element => {
                const expiresAt = parseInt(element.getAttribute('data-expires-at'));
                const invitationId = element.getAttribute('data-invitation-id');
                const timerElement = document.getElementById(`timer-${invitationId}`);
                
                if (!timerElement) return;
                
                const now = Math.floor(Date.now() / 1000);
                const timeLeft = expiresAt - now;
                
                if (timeLeft <= 0) {
                    // Приглашение истекло
                    element.style.opacity = '0.5';
                    timerElement.textContent = '00:00';
                    timerElement.classList.add('critical');
                    
                    // Отключаем кнопки
                    element.querySelectorAll('button').forEach(btn => {
                        btn.disabled = true;
                    });
                    
                    // Скрываем через 2 секунды
                    setTimeout(() => {
                        element.style.display = 'none';
                    }, 2000);
                    
                    return;
                }
                
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                timerElement.textContent = timeString;
                
                // Меняем цвет в зависимости от времени
                timerElement.classList.remove('warning', 'critical');
                if (timeLeft <= 10) {
                    timerElement.classList.add('critical');
                } else if (timeLeft <= 30) {
                    timerElement.classList.add('warning');
                }
            });
        }
        
        // Обработка действий с приглашением
        function handleInvitationAction(form, action) {
            const button = form.querySelector('button');
            const originalText = button.innerHTML;
            
            // Показываем загрузку
            button.disabled = true;
            button.innerHTML = `<span class="animate-spin">⟳</span> ${action === 'accept' ? 'Принятие...' : 'Отклонение...'}`;
            
            // Если что-то пойдет не так, восстанавливаем кнопку через 5 секунд
            setTimeout(() => {
                button.disabled = false;
                button.innerHTML = originalText;
            }, 5000);
            
            return true;
        }
        
        // Запускаем обновление таймера каждую секунду
        setInterval(updateInvitationTimers, 1000);
        
        // Инициализируем при загрузке
        document.addEventListener('DOMContentLoaded', updateInvitationTimers);
    </script>
    <?php $__env->stopPush(); ?>
    <?php endif; ?>
<?php endif; ?><?php /**PATH C:\Users\<USER>\Desktop\phpProject\resources\views/components/party-invitation-notification.blade.php ENDPATH**/ ?>