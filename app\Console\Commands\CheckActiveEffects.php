<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\ActiveEffect;

class CheckActiveEffects extends Command
{
    protected $signature = 'debug:active-effects {user=admin}';
    protected $description = 'Проверяет активные эффекты пользователя';

    public function handle()
    {
        $userName = $this->argument('user');
        $user = User::where('name', $userName)->first();
        
        if (!$user) {
            $this->error("❌ Пользователь '{$userName}' не найден!");
            return 1;
        }

        $this->info("🔍 Проверка активных эффектов для пользователя: {$user->name} (ID: {$user->id})");

        // Получаем все эффекты пользователя
        $allEffects = $user->activeEffects()->with('skill')->get();
        $this->info("📋 Всего эффектов в БД: {$allEffects->count()}");

        if ($allEffects->isEmpty()) {
            $this->warn('⚠️ У пользователя нет эффектов в базе данных');
            return 0;
        }

        // Проверяем каждый эффект
        foreach ($allEffects as $effect) {
            $this->line('');
            $this->info("🎯 Эффект ID: {$effect->id}");
            $this->info("   Тип: {$effect->effect_type}");
            $this->info("   Skill ID: " . ($effect->skill_id ?? 'null'));
            $this->info("   Создан: {$effect->created_at}");
            $this->info("   Заканчивается: " . ($effect->ends_at ?? 'не указано'));
            $this->info("   Длительность: {$effect->duration}с");
            
            // Проверяем активность
            $isActive = $effect->isActive();
            $this->info("   Активен: " . ($isActive ? '✅ ДА' : '❌ НЕТ'));
            
            // Проверяем, является ли стан-эффектом
            $isStun = $effect->isStunEffect();
            $this->info("   Стан-эффект: " . ($isStun ? '✅ ДА' : '❌ НЕТ'));
            
            // Показываем данные эффекта
            if ($effect->effect_data) {
                $this->info("   Данные: " . json_encode($effect->effect_data, JSON_UNESCAPED_UNICODE));
            }
            
            // Показываем связанный скилл
            if ($effect->skill) {
                $this->info("   Скилл: {$effect->skill->name}");
            } else {
                $this->warn("   ⚠️ Скилл не найден");
            }
        }

        // Фильтруем только активные эффекты
        $activeEffects = $allEffects->filter(function ($effect) {
            return $effect->isActive();
        });

        $this->line('');
        $this->info("✅ Активных эффектов: {$activeEffects->count()}");

        // Проверяем состояние стана
        $isStunned = $activeEffects->contains(function ($effect) {
            return $effect->isStunEffect();
        });

        $this->info("🔒 Игрок оглушен: " . ($isStunned ? '✅ ДА' : '❌ НЕТ'));

        return 0;
    }
}
