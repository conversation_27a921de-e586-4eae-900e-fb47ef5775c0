<?php

namespace App\Console\Commands;

use App\Models\ActiveEffect;
use App\Models\User;
use Illuminate\Console\Command;

class CheckActiveEffectsDB extends Command
{
    protected $signature = 'effects:check-db {player_name}';
    protected $description = 'Проверяет активные эффекты в базе данных';

    public function handle()
    {
        $playerName = $this->argument('player_name');

        $player = User::where('name', $playerName)->first();
        if (!$player) {
            $this->error("Игрок '{$playerName}' не найден!");
            return 1;
        }

        $this->info("Проверка активных эффектов для игрока: {$player->name} (ID: {$player->id})");

        // Проверяем все эффекты для игрока (включая истекшие)
        $allEffects = ActiveEffect::where('target_type', 'App\\Models\\User')
            ->where('target_id', $player->id)
            ->orderBy('created_at', 'desc')
            ->take(10)
            ->get();

        $this->info("Всего эффектов в БД (последние 10): " . $allEffects->count());

        foreach ($allEffects as $effect) {
            $isActive = $effect->ends_at > now();
            $status = $isActive ? '✅ АКТИВЕН' : '❌ ИСТЕК';
            
            $this->info("Эффект ID: {$effect->id} {$status}");
            $this->info("  Тип: {$effect->effect_type}");
            $this->info("  Длительность: {$effect->duration} сек");
            $this->info("  Создан: {$effect->created_at}");
            $this->info("  Заканчивается: {$effect->ends_at}");
            $this->info("  Сейчас: " . now());
            $this->info("  Источник: {$effect->source_type} ID: {$effect->source_id}");
            $this->info("  Данные: " . json_encode($effect->effect_data, JSON_UNESCAPED_UNICODE));
            $this->info("");
        }

        // Проверяем только активные эффекты
        $activeEffects = ActiveEffect::where('target_type', 'App\\Models\\User')
            ->where('target_id', $player->id)
            ->where('ends_at', '>', now())
            ->get();

        $this->info("Активных эффектов сейчас: " . $activeEffects->count());

        return 0;
    }
}
