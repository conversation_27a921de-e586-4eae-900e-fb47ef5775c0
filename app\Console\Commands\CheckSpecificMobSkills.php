<?php

namespace App\Console\Commands;

use App\Models\Mob;
use App\Models\MobSkill;
use App\Models\MobSkillTemplate;
use Illuminate\Console\Command;

class CheckSpecificMobSkills extends Command
{
    protected $signature = 'mob:check-specific-skills {mob_id}';
    protected $description = 'Проверяет скиллы конкретного моба';

    public function handle()
    {
        $mobId = $this->argument('mob_id');

        $mob = Mob::find($mobId);
        if (!$mob) {
            $this->error("Моб с ID '{$mobId}' не найден!");
            return 1;
        }

        $this->info("Проверка скиллов моба: {$mob->name} (ID: {$mob->id})");
        $this->info("Тип: {$mob->mob_type}");
        $this->info("Локация ID: {$mob->location_id}");
        $this->info("Mine Location ID: {$mob->mine_location_id}");
        $this->info("");

        // Проверяем скиллы моба
        $skills = $mob->skills()->with('skillTemplate')->get();
        
        $this->info("Найдено скиллов: " . $skills->count());
        
        if ($skills->isEmpty()) {
            $this->info("У моба нет скиллов!");
            
            // Предлагаем привязать скилл
            $this->info("\nПривязываем скилл 'Тяжелый удар'...");
            
            $template = MobSkillTemplate::where('name', 'Тяжелый удар')->first();
            if ($template) {
                $mobSkill = MobSkill::create([
                    'mob_id' => $mob->id,
                    'skill_template_id' => $template->id,
                ]);
                
                $this->info("✅ Скилл привязан! ID: {$mobSkill->id}");
            } else {
                $this->error("Шаблон скилла 'Тяжелый удар' не найден!");
            }
        } else {
            foreach ($skills as $skill) {
                $template = $skill->skillTemplate;
                $this->info("Скилл ID: {$skill->id}");
                $this->info("  Template ID: {$template->id}");
                $this->info("  Название: {$template->name}");
                $this->info("  Тип эффекта: {$template->effect_type}");
                $this->info("  Шанс: {$template->chance}%");
                $this->info("  Длительность: {$template->duration} сек");
                $this->info("  Кулдаун заканчивается: " . ($skill->cooldown_ends_at ?? 'Нет'));
                $this->info("");
            }
        }

        return 0;
    }
}
