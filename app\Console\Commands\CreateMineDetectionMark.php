<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\MineLocation;
use App\Services\MineDetectionService;

class CreateMineDetectionMark extends Command
{
    protected $signature = 'mine:create-detection-mark {username} {mine_name}';
    protected $description = 'Создать метку обнаружения для игрока в руднике';

    public function handle()
    {
        $username = $this->argument('username');
        $mineName = $this->argument('mine_name');

        // Находим пользователя
        $user = User::where('name', $username)->first();
        if (!$user) {
            $this->error("Пользователь '{$username}' не найден");
            return 1;
        }

        // Находим рудник
        $mineLocation = MineLocation::where('name', $mineName)->first();
        if (!$mineLocation) {
            $this->error("Рудник '{$mineName}' не найден");
            return 1;
        }

        $this->info("Создание метки обнаружения:");
        $this->info("  Игрок: {$user->name} (ID: {$user->id})");
        $this->info("  Рудник: {$mineLocation->name} (ID: {$mineLocation->id})");

        // Создаем метку через сервис
        $mineDetectionService = app(MineDetectionService::class);
        
        try {
            $mark = $mineDetectionService->applyDetectionDebuff($user, $mineLocation);
            
            if ($mark) {
                $this->info("✅ Метка создана успешно:");
                $this->info("  ID метки: {$mark->id}");
                $this->info("  Истекает: {$mark->expires_at}");
                $this->info("  Активна: " . ($mark->is_active ? 'Да' : 'Нет'));
            } else {
                $this->error("❌ Не удалось создать метку");
                return 1;
            }
        } catch (\Exception $e) {
            $this->error("❌ Ошибка при создании метки: " . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
