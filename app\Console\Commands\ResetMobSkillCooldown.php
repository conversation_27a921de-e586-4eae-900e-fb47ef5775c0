<?php

namespace App\Console\Commands;

use App\Models\MobSkill;
use Illuminate\Console\Command;

class ResetMobSkillCooldown extends Command
{
    protected $signature = 'mob:reset-cooldown {mob_id}';
    protected $description = 'Сбрасывает кулдауны скиллов моба';

    public function handle()
    {
        $mobId = $this->argument('mob_id');

        $skills = MobSkill::where('mob_id', $mobId)->get();
        
        if ($skills->isEmpty()) {
            $this->error("У моба с ID '{$mobId}' нет скиллов!");
            return 1;
        }

        $this->info("Сбрасываем кулдауны для моба ID: {$mobId}");
        
        foreach ($skills as $skill) {
            $skill->update([
                'cooldown_ends_at' => null,
                'last_used_at' => null
            ]);
            
            $this->info("✅ Кулдаун сброшен для скилла ID: {$skill->id}");
        }

        return 0;
    }
}
