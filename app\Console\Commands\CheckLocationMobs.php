<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Mob;
use App\Models\Location;
use App\Models\MineLocation;

class CheckLocationMobs extends Command
{
    protected $signature = 'debug:location-mobs {location=аааааааааааа}';
    protected $description = 'Проверяет мобов в указанной локации';

    public function handle()
    {
        $locationName = $this->argument('location');
        
        $this->info("🔍 Проверка мобов в локации: {$locationName}");

        // Ищем локацию
        $location = Location::where('name', $locationName)->first();
        if (!$location) {
            $this->error("❌ Локация '{$locationName}' не найдена!");
            return 1;
        }

        $this->info("✅ Локация найдена: ID={$location->id}, Название={$location->name}");

        // Ищем рудник в этой локации
        $mineLocation = MineLocation::where('location_id', $location->id)->first();
        if ($mineLocation) {
            $this->info("⛏️ Рудник найден: ID={$mineLocation->id}, Название={$mineLocation->name}");
        } else {
            $this->warn("⚠️ Рудник в этой локации не найден");
        }

        // Ищем мобов в локации
        $mobs = Mob::where('location', $locationName)
            ->orWhere('location_id', $location->id)
            ->get();

        $this->info("👹 Мобы в локации ({$mobs->count()}):");
        
        if ($mobs->isEmpty()) {
            $this->warn('⚠️ Мобы в локации не найдены!');
            return 0;
        }

        foreach ($mobs as $mob) {
            $this->line('');
            $this->info("🎯 Моб: {$mob->name} (ID: {$mob->id})");
            $this->info("   Локация: {$mob->location}");
            $this->info("   Location ID: " . ($mob->location_id ?? 'null'));
            $this->info("   Тип: {$mob->mob_type}");
            $this->info("   HP: {$mob->hp}/{$mob->max_hp}");
            $this->info("   Сила: {$mob->strength}");

            // Проверяем скиллы моба
            $skills = $mob->skills()->with('skillTemplate')->get();
            $this->info("   Скиллы ({$skills->count()}):");
            
            if ($skills->isEmpty()) {
                $this->warn("     ⚠️ У моба нет скиллов!");
            } else {
                foreach ($skills as $skill) {
                    $template = $skill->skillTemplate;
                    if ($template) {
                        $this->info("     - {$template->name} (Шанс: {$skill->chance}%, Кулдаун: {$template->cooldown}с)");
                        $this->info("       Тип: {$template->effect_type}, Активен: " . ($template->is_active ? 'Да' : 'Нет'));
                        
                        // Проверяем кулдаун
                        if ($skill->cooldown_ends_at && $skill->cooldown_ends_at->isFuture()) {
                            $this->warn("       ⏰ На кулдауне до: {$skill->cooldown_ends_at}");
                        } else {
                            $this->info("       ✅ Готов к использованию");
                        }
                    } else {
                        $this->warn("     - Скилл ID {$skill->skill_template_id} (шаблон не найден)");
                    }
                }
            }
        }

        return 0;
    }
}
