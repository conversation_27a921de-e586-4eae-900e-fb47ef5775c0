<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\ActiveEffect;
use App\Models\Location;
use App\Models\MineLocation;
use App\Models\MineMark;

class CreateMineDetectionDebuff extends Command
{
    protected $signature = 'debug:create-mine-detection {user=admin} {location=аааааааааааа}';
    protected $description = 'Создает дебафф "Замечен" для тестирования атак мобов';

    public function handle()
    {
        $userName = $this->argument('user');
        $locationName = $this->argument('location');

        $user = User::where('name', $userName)->first();
        if (!$user) {
            $this->error("❌ Пользователь '{$userName}' не найден!");
            return 1;
        }

        $location = Location::where('name', $locationName)->first();
        if (!$location) {
            $this->error("❌ Локация '{$locationName}' не найдена!");
            return 1;
        }

        $mineLocation = MineLocation::where('location_id', $location->id)->first();
        if (!$mineLocation) {
            $this->error("❌ Рудник в локации '{$locationName}' не найден!");
            return 1;
        }

        $this->info("🔍 Создание дебаффа 'Замечен' для пользователя: {$user->name}");
        $this->info("📍 Локация: {$locationName} (ID: {$location->id})");
        $this->info("⛏️ Рудник: {$mineLocation->name} (ID: {$mineLocation->id})");

        // Удаляем старые дебаффы "Замечен"
        $oldEffects = $user->activeEffects()
            ->where('effect_type', 'mine_detection')
            ->get();

        foreach ($oldEffects as $effect) {
            $effect->delete();
            $this->info("🗑️ Удален старый дебафф ID: {$effect->id}");
        }

        // Создаем новый дебафф
        $effect = ActiveEffect::create([
            'target_type' => 'player',
            'target_id' => $user->id,
            'effect_type' => 'mine_detection',
            'duration' => 300, // 5 минут
            'effect_data' => [
                'mine_location_id' => $mineLocation->id,
                'location_id' => $location->id,
                'detected_at' => now()->toISOString()
            ]
        ]);

        $this->info("✅ Создан дебафф 'Замечен' ID: {$effect->id}");
        $this->info("⏰ Длительность: {$effect->duration} секунд");
        $this->info("🕐 Заканчивается: {$effect->ends_at}");

        // Проверяем активность
        $isActive = $effect->isActive();
        $this->info("🔍 Активен: " . ($isActive ? '✅ ДА' : '❌ НЕТ'));

        // Создаем также метку рудника для системы атак мобов
        $this->info("🎯 Создание метки рудника...");

        // Удаляем старые метки
        $oldMarks = MineMark::where('player_id', $user->id)
            ->where('mine_location_id', $mineLocation->id)
            ->get();

        foreach ($oldMarks as $mark) {
            $mark->delete();
            $this->info("🗑️ Удалена старая метка рудника ID: {$mark->id}");
        }

        // Создаем новую метку
        $mark = MineMark::create([
            'player_id' => $user->id,
            'mine_location_id' => $mineLocation->id,
            'location_id' => $location->id,
            'location_name' => $locationName,
            'expires_at' => now()->addMinutes(5),
            'is_active' => true,
            'attack_count' => 0
        ]);

        $this->info("✅ Создана метка рудника ID: {$mark->id}");
        $this->info("⏰ Истекает: {$mark->expires_at}");

        return 0;
    }
}
