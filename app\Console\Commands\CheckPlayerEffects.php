<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\ActiveEffect;

class CheckPlayerEffects extends Command
{
    protected $signature = 'player:check-effects {username}';
    protected $description = 'Проверить активные эффекты игрока';

    public function handle()
    {
        $username = $this->argument('username');

        // Находим пользователя
        $user = User::where('name', $username)->first();
        if (!$user) {
            $this->error("Пользователь '{$username}' не найден");
            return 1;
        }

        $this->info("Проверка активных эффектов для игрока: {$user->name} (ID: {$user->id})");

        if ($user->profile) {
            $this->info("HP: {$user->profile->current_hp}/{$user->profile->max_hp}");
        }

        // Получаем активные эффекты
        $effects = ActiveEffect::where('target_type', 'App\\Models\\User')
            ->where('target_id', $user->id)
            ->where(function ($query) {
                $query->where('ends_at', '>', now())
                    ->orWhere(function ($q) {
                        $q->whereNull('ends_at')
                            ->whereRaw('EXTRACT(EPOCH FROM (NOW() - created_at)) < duration');
                    });
            })
            ->get();

        $this->info("Активные эффекты: " . $effects->count());

        if ($effects->count() > 0) {
            foreach ($effects as $effect) {
                $effectData = $effect->effect_data;
                $effectType = $effect->effect_type ?? 'unknown';
                $effectValue = $effect->power ?? 'N/A';
                $endsAt = $effect->ends_at ? $effect->ends_at->format('Y-m-d H:i:s') : 'не установлено';

                $this->info("  - {$effectType}: {$effectValue} (истекает: {$endsAt})");

                // Показываем все доступные атрибуты
                $attributes = $effect->getAttributes();
                foreach ($attributes as $key => $value) {
                    if (!in_array($key, ['created_at', 'updated_at', 'effect_data'])) {
                        $this->info("    {$key}: {$value}");
                    }
                }

                if ($effectData) {
                    $this->info("    Data: " . json_encode($effectData));
                }
                $this->info("    ---");
            }
        } else {
            $this->info("  Активных эффектов нет");
        }

        return 0;
    }
}
