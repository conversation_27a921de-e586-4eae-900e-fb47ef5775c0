<?php

namespace App\Console\Commands;

use App\Models\ActiveEffect;
use App\Models\User;
use Illuminate\Console\Command;

class TestActiveEffectCreation extends Command
{
    protected $signature = 'test:active-effect-creation';
    protected $description = 'Тестирует создание ActiveEffect';

    public function handle()
    {
        $this->info("🧪 Тестирование создания ActiveEffect...");

        $player = User::where('name', 'admin')->first();
        if (!$player) {
            $this->error("Игрок admin не найден!");
            return 1;
        }

        $this->info("Игрок найден: {$player->name} (ID: {$player->id})");

        // Очищаем старые тестовые эффекты
        ActiveEffect::where('target_type', 'App\\Models\\User')
            ->where('target_id', $player->id)
            ->where('effect_type', 'test_stun')
            ->delete();

        $this->info("Старые тестовые эффекты очищены");

        // Создаем тестовый эффект
        $this->info("Создаем тестовый эффект...");

        try {
            $effect = ActiveEffect::create([
                'effect_type' => 'test_stun',
                'target_type' => 'App\\Models\\User',
                'target_id' => $player->id,
                'source_type' => 'App\\Models\\Mob',
                'source_id' => 14,
                'skill_id' => null,
                'duration' => 10,
                'ends_at' => now()->addSeconds(10),
                'power' => 1,
                'effect_data' => [
                    'type' => 'test_stun',
                    'message' => 'Тестовый эффект оглушения'
                ]
            ]);

            $this->info("✅ Эффект создан успешно!");
            $this->info("  ID: {$effect->id}");
            $this->info("  Тип: {$effect->effect_type}");
            $this->info("  Цель: {$effect->target_id}");
            $this->info("  Длительность: {$effect->duration} сек");
            $this->info("  Заканчивается: {$effect->ends_at}");

            // Проверяем, что эффект сохранился
            $savedEffect = ActiveEffect::find($effect->id);
            if ($savedEffect) {
                $this->info("✅ Эффект найден в базе данных");
            } else {
                $this->error("❌ Эффект НЕ найден в базе данных!");
            }

            // Проверяем активные эффекты игрока
            $activeEffects = ActiveEffect::where('target_type', 'App\\Models\\User')
                ->where('target_id', $player->id)
                ->where('ends_at', '>', now())
                ->get();

            $this->info("Активных эффектов у игрока: " . $activeEffects->count());

            foreach ($activeEffects as $activeEffect) {
                $this->info("  - ID: {$activeEffect->id}, Тип: {$activeEffect->effect_type}, Заканчивается: {$activeEffect->ends_at}");
            }

        } catch (\Exception $e) {
            $this->error("❌ Ошибка при создании эффекта: " . $e->getMessage());
            $this->error("Стек: " . $e->getTraceAsString());
        }

        return 0;
    }
}
