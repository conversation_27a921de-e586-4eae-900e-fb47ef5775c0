<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CheckMobSkillLogs extends Command
{
    protected $signature = 'mob:check-skill-logs {--lines=50}';
    protected $description = 'Проверяет последние логи скиллов мобов';

    public function handle()
    {
        $lines = (int) $this->option('lines');

        $this->info("Проверка последних {$lines} строк логов скиллов мобов...\n");

        // Читаем лог файл
        $logPath = storage_path('logs/laravel.log');

        if (!file_exists($logPath)) {
            $this->error("Лог файл не найден: {$logPath}");
            return 1;
        }

        // Читаем файл и получаем последние строки
        $fileContent = file_get_contents($logPath);
        if (!$fileContent) {
            $this->error("Не удалось прочитать лог файл");
            return 1;
        }

        // Получаем последние строки
        $logLines = explode("\n", $fileContent);
        $logLines = array_slice($logLines, -$lines);
        $skillLogs = [];

        foreach ($logLines as $line) {
            if (
                strpos($line, 'MobSkillFramework') !== false ||
                strpos($line, 'MobSkill') !== false ||
                strpos($line, 'ActiveEffect') !== false ||
                strpos($line, 'stun') !== false
            ) {
                $skillLogs[] = $line;
            }
        }

        if (empty($skillLogs)) {
            $this->info("Логи скиллов мобов не найдены в последних {$lines} строках");
            return 0;
        }

        $this->info("Найдено " . count($skillLogs) . " записей о скиллах мобов:\n");

        foreach ($skillLogs as $log) {
            $this->line($log);
        }

        return 0;
    }
}
