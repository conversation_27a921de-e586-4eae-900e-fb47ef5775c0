<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MobSkillTemplate;

class CheckMobSkillTemplate extends Command
{
    protected $signature = 'mob:check-skill-template {template_id}';
    protected $description = 'Проверить шаблон скилла моба';

    public function handle()
    {
        $templateId = $this->argument('template_id');

        // Находим шаблон
        $template = MobSkillTemplate::find($templateId);
        if (!$template) {
            $this->error("Шаблон скилла с ID '{$templateId}' не найден");
            return 1;
        }

        $this->info("Проверка шаблона скилла: {$template->name} (ID: {$template->id})");
        $this->info("Описание: {$template->description}");
        $this->info("Тип эффекта: {$template->effect_type}");
        $this->info("Шанс срабатывания: {$template->trigger_chance}%");
        $this->info("Кулдаун: {$template->cooldown} сек");
        $this->info("Длительность эффекта: {$template->effect_duration} сек");
        $this->info("Значение эффекта: {$template->effect_value}");
        $this->info("Порог здоровья: {$template->health_threshold}%");
        
        if ($template->effect_data) {
            $this->info("Дополнительные данные эффекта:");
            if (is_array($template->effect_data)) {
                foreach ($template->effect_data as $key => $value) {
                    $this->info("  {$key}: {$value}");
                }
            } else {
                $this->info("  " . json_encode($template->effect_data));
            }
        }

        return 0;
    }
}
